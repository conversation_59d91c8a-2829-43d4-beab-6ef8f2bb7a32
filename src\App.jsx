import { useState } from 'react';
import SearchBar from './components/SearchBar';
import BookList from './components/BookList';
import LoadMoreButton from './components/LoadMoreButton';
import { searchBooks } from './api.js';

function App() {
  const [books, setBooks] = useState([]);
  const [numFound, setNumFound] = useState(0);
  const [query, setQuery] = useState('');
  const [searchType, setSearchType] = useState('title');
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [hasSearched, setHasSearched] = useState(false);

  const handleSearch = async (searchQuery, type) => {
    setQuery(searchQuery);
    setSearchType(type);
    setPage(1); // reset page
    setLoading(true);
    setError('');
    setHasSearched(true);

    try {
      const data = await searchBooks(searchQuery, type, 1);
      setBooks(data.docs || []); // fresh results
      setNumFound(data.numFound || 0);
    } catch (err) {
      setError('Failed to fetch books. Try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleLoadMore = async () => {
    const nextPage = page + 1;
    setLoading(true);

    try {
      const data = await searchBooks(query, searchType, nextPage);
      setBooks((prev) => [...prev, ...(data.docs || [])]); // append results
      setPage(nextPage);
    } catch (err) {
      setError('Failed to load more results.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4 font-sans">
      <h1 className="text-4xl font-extrabold text-center mb-8 text-blue-700 tracking-tight">
        📚 Open Library Book Finder
      </h1>

      <SearchBar onSearch={handleSearch} loading={loading} />

      {error && <p className="text-center text-red-600 mt-4">{error}</p>}

      {/* Spinner */}
      {loading && page === 1 && (
        <div className="flex justify-center mt-10">
          <div className="w-10 h-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}

      {/* Results */}
      {!loading && hasSearched && books.length === 0 && (
        <p className="text-center text-gray-600 mt-6">No results found.</p>
      )}

      {books.length > 0 && <BookList books={books} />}

      {/* Load More */}
      {books.length > 0 && books.length < numFound && (
        <div className="flex justify-center mt-6">
          <LoadMoreButton onClick={handleLoadMore} loading={loading} />
        </div>
      )}
    </div>
  );
}

export default App;
