const BASE_URL = 'https://openlibrary.org/search.json';

// Helper function to search books
export const searchBooks = async (query, searchType = 'title', page = 1) => {
  try {
    const offset = (page - 1) * 20; // API returns 20 per page
    const url = `${BASE_URL}?${searchType}=${encodeURIComponent(
      query
    )}&offset=${offset}`;
    console.log(url);
    const response = await fetch(url);
    if (!response.ok) throw new Error('Failed to fetch books');

    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
};
