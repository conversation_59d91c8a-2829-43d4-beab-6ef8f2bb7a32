import { useState, useEffect, useRef } from "react";

const Autocomplete = ({ query, setQuery, searchType, onSelect }) => {
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const wrapperRef = useRef(null);
  const timeoutRef = useRef(null);

  // Fetch suggestions
  useEffect(() => {
    if (!query.trim()) {
      setSuggestions([]);
      setOpen(false);
      return;
    }

    clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(async () => {
      try {
        setLoading(true);
        const res = await fetch(
          `https://openlibrary.org/search.json?${searchType}=${encodeURIComponent(
            query
          )}&limit=5`
        );
        const data = await res.json();
        setSuggestions(data.docs || []);
        setOpen(true);
      } catch (err) {
        console.error("Error fetching suggestions:", err);
        setSuggestions([]);
      } finally {
        setLoading(false);
      }
    }, 400);

    return () => clearTimeout(timeoutRef.current);
  }, [query, searchType]);

  // Close on outside click
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target)) {
        setOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleSelect = (book) => {
    const displayText = book.title || book.author_name?.[0] || query;
    setQuery(displayText);
    setOpen(false);
    onSelect?.(book); // Pass full book object
  };

  const handleInputFocus = () => {
    if (query.trim() && suggestions.length > 0) {
      setOpen(true);
    }
  };

  return (
    <div className="relative w-full" ref={wrapperRef}>
      <input
        type="text"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        onFocus={handleInputFocus}
        placeholder={`Search by ${searchType}...`}
        className="w-full px-4 py-2 h-12 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
      />

      {open && (
        <ul className="absolute z-10 mt-1 w-full bg-white border rounded-lg shadow-lg max-h-60 overflow-y-auto">
          {loading && <li className="px-4 py-2 text-gray-500">Searching...</li>}
          {!loading && suggestions.length === 0 && (
            <li className="px-4 py-2 text-gray-500">No results found</li>
          )}
          {suggestions.map((book, idx) => (
            <li
              key={idx}
              onClick={() => handleSelect(book)}
              className="px-4 py-2 cursor-pointer hover:bg-gray-100"
            >
              <span className="font-medium">{book.title}</span>
              {book.author_name && (
                <span className="text-gray-600 text-sm block">
                  by {book.author_name[0]}
                </span>
              )}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default Autocomplete;