import { ExternalLink } from 'lucide-react';

export default function BookCard({ book }) {
  const {
    cover_i,
    cover_edition_key,
    key,
    title,
    author_name,
    first_publish_year,
    edition_count,
    isbn,
    subject,
  } = book;

  // Construct correct Open Library URL
  const detailUrl = key
    ? `https://openlibrary.org${key}`
    : 'https://openlibrary.org';

  // Try multiple cover sources
  const coverUrl = cover_i
    ? `https://covers.openlibrary.org/b/id/${cover_i}-M.jpg`
    : cover_edition_key
    ? `https://covers.openlibrary.org/b/olid/${cover_edition_key}-M.jpg`
    : null;

  // Fallback UI for no image
  const FallbackUI = () => (
    <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200 text-gray-500 text-xs font-medium text-center p-2">
      No Cover
    </div>
  );

  // Safely format data
  const authorNames =
    author_name?.length > 0 ? author_name.join(', ') : 'Unknown Author';
  const firstPublishYear = first_publish_year || 'N/A';
  const editionCount = edition_count || 'N/A';
  const isbnDisplay = isbn?.[0] || 'N/A';
  const subjectDisplay = subject?.[0] || 'General Literature';

  // Fix URL spacing issue
  const cleanDetailUrl = detailUrl.replace(/\s+/g, '');

  return (
    <div className="relative bg-white rounded-xl shadow-sm hover:shadow-lg border border-gray-100 p-5 flex gap-4 transition-all duration-200 select-text">
      {/* Cover */}
      <div className="flex-shrink-0 relative w-24 h-36">
        {coverUrl ? (
          <img
            src={coverUrl}
            alt={title || 'Book cover'}
            referrerPolicy="no-referrer"
            crossOrigin="anonymous"
            className="w-full h-full object-cover rounded-md border border-gray-200"
            draggable="false"
            onError={(e) => {
              const fallback = document.createElement('div');
              fallback.innerHTML =
                '<div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200 text-gray-500 text-xs font-medium text-center p-2">No Cover</div>';
              e.target.replaceWith(fallback.firstElementChild);
            }}
          />
        ) : (
          <FallbackUI />
        )}
        {editionCount && (
          <span className="absolute -top-2 -right-2 bg-blue-600 text-white text-xs font-bold px-2 py-1 rounded-full text-xxs leading-tight shadow">
            {editionCount}
          </span>
        )}
      </div>

      {/* Book Info */}
      <div className="flex-1 min-w-0">
        <h2 className="text-lg font-semibold text-gray-900 line-clamp-2 mb-1">
          {title || 'Untitled Book'}
        </h2>
        <p className="text-sm text-gray-700 mb-2 line-clamp-1">
          by <span className="font-medium">{authorNames}</span>
        </p>
        <div className="text-xs text-gray-500 space-y-1 mt-1">
          <p>📅 First published: {firstPublishYear}</p>
          <p>
            📚 {editionCount} edition{editionCount !== 1 ? 's' : ''}
          </p>
          {isbnDisplay !== 'N/A' && <p>🔖 ISBN: {isbnDisplay}</p>}
          <p>🏷️ Subject: {subjectDisplay}</p>
        </div>
      </div>

      {/* External Link Icon */}
      <a
        href={cleanDetailUrl}
        target="_blank"
        rel="noopener noreferrer"
        className="absolute bottom-3 right-3 p-1.5 text-blue-500 hover:text-blue-600 rounded-full hover:bg-blue-50 transition-colors duration-200"
        onClick={(e) => {
          e.stopPropagation();
        }}
        aria-label="View book details on Open Library"
      >
        <ExternalLink className="w-4 h-4" />
      </a>
    </div>
  );
}
