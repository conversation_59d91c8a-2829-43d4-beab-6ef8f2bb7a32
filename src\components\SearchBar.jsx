import { useState, useEffect, useRef } from 'react';
import { Search, ChevronDown, Loader2 } from 'lucide-react';

const SEARCH_OPTIONS = [
  { value: 'title', label: 'Title' },
  { value: 'author', label: 'Author' },
  { value: 'subject', label: 'Subject' },
  { value: 'isbn', label: 'ISBN' },
];

const SearchBar = ({ onSearch, loading }) => {
  const [query, setQuery] = useState('');
  const [searchType, setSearchType] = useState('title');
  const [openDropdown, setOpenDropdown] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const [loadingSuggestions, setLoadingSuggestions] = useState(false);

  const containerRef = useRef(null);

  // Fetch suggestions
  useEffect(() => {
    if (query.trim().length < 3) {
      setSuggestions([]);
      return;
    }

    const controller = new AbortController();
    setLoadingSuggestions(true);

    const timeout = setTimeout(async () => {
      try {
        const res = await fetch(
          `https://openlibrary.org/search.json?${searchType}=${encodeURIComponent(
            query
          )}&limit=5`,
          { signal: controller.signal }
        );
        const data = await res.json();
        setSuggestions(data.docs || []);
      } catch (err) {
        if (err.name !== 'AbortError') console.error(err);
      } finally {
        setLoadingSuggestions(false);
      }
    }, 300);

    return () => {
      clearTimeout(timeout);
      controller.abort();
    };
  }, [query, searchType]);

  // Close dropdowns on outside click
  useEffect(() => {
    const handleClickOutside = (e) => {
      if (containerRef.current && !containerRef.current.contains(e.target)) {
        setOpenDropdown(false);
        setSuggestions([]);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Submit search
  const handleSubmit = (e) => {
    e.preventDefault();
    if (!query.trim()) return;
    onSearch(query, searchType);
    setSuggestions([]);
  };

  // Select suggestion
  const handleSelect = (book) => {
    const value =
      searchType === 'author'
        ? book.author_name?.[0]
        : searchType === 'isbn'
        ? book.isbn?.[0]
        : book.title;
    setQuery(value || query);
    setSuggestions([]);
    onSearch(value || query, searchType);
  };

  return (
    <form
      ref={containerRef}
      onSubmit={handleSubmit}
      className="flex flex-col sm:flex-row items-stretch sm:items-center justify-center gap-3 max-w-4xl mx-auto mb-8 px-4 relative"
    >
      {/* Dropdown */}
      <div className="relative w-full sm:w-40">
        <button
          type="button"
          onClick={() => setOpenDropdown(!openDropdown)}
          className="w-full h-12 px-4 flex items-center justify-between border rounded-lg bg-white focus:ring-2 focus:ring-blue-500"
        >
          <span>
            {SEARCH_OPTIONS.find((o) => o.value === searchType)?.label}
          </span>
          <ChevronDown
            className={`w-4 h-4 transition-transform ${
              openDropdown ? 'rotate-180' : ''
            }`}
          />
        </button>
        {openDropdown && (
          <div className="absolute z-50 mt-1 w-full bg-white border rounded-lg shadow-lg">
            {SEARCH_OPTIONS.map((option) => (
              <div
                key={option.value}
                onClick={() => {
                  setSearchType(option.value);
                  setOpenDropdown(false);
                }}
                className={`px-4 py-2 cursor-pointer hover:bg-blue-100 ${
                  searchType === option.value ? 'bg-blue-50 font-medium' : ''
                }`}
              >
                {option.label}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Input + suggestions */}
      <div className="relative flex-1">
        <input
          type="text"
          placeholder={`Search by ${searchType}...`}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className="w-full px-4 py-2 h-12 border rounded-lg focus:ring-2 focus:ring-blue-500"
        />

        {suggestions.length > 0 && (
          <ul className="absolute top-full left-0 w-full bg-white border rounded-lg shadow-lg mt-1 z-50">
            {loadingSuggestions && (
              <li className="px-4 py-2 text-gray-500 flex items-center gap-2">
                <Loader2 className="w-4 h-4 animate-spin" /> Searching...
              </li>
            )}
            {!loadingSuggestions &&
              suggestions.map((book) => (
                <li
                  key={book.key}
                  onClick={() => handleSelect(book)}
                  className="px-4 py-2 cursor-pointer hover:bg-blue-100"
                >
                  <span className="font-medium">{book.title}</span>
                  {book.author_name && (
                    <span className="block text-sm text-gray-600">
                      by {book.author_name[0]}
                    </span>
                  )}
                </li>
              ))}
          </ul>
        )}
      </div>

      {/* Search button */}
      <button
        type="submit"
        disabled={loading}
        className="h-11 px-5 bg-blue-600 text-white flex items-center justify-center gap-2 rounded-lg hover:bg-blue-700 active:scale-95 disabled:opacity-60"
      >
        {loading ? (
          // <Loader2 className="w-5 h-5 animate-spin" />
          ''
        ) : (
          <Search className="w-5 h-5" />
        )}
        <span>{loading ? 'Searching...' : 'Search'}</span>
      </button>
    </form>
  );
};

export default SearchBar;
